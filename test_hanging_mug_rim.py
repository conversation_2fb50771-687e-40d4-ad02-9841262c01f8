#!/usr/bin/env python3
"""
测试挂杯口任务的简单脚本
"""
import sys
sys.path.append("./")

import sapien.core as sapien
from envs.hanging_mug_rim import hanging_mug_rim
import yaml

def test_hanging_mug_rim_task():
    """测试挂杯口任务是否正常工作"""
    print("Testing hanging_mug_rim task...")
    
    try:
        # 创建任务实例
        task = hanging_mug_rim()
        print("✓ Task instance created successfully")
        
        # 加载配置
        with open("task_config/hanging_mug_rim_demo.yml", "r") as f:
            config = yaml.load(f.read(), Loader=yaml.FullLoader)
        
        config['task_name'] = 'hanging_mug_rim'
        config['embodiment'] = ['aloha-agilex']
        
        print("✓ Configuration loaded successfully")
        
        # 设置演示环境
        task.setup_demo(**config)
        print("✓ Demo environment setup completed")
        
        # 执行一次任务
        result = task.play_once()
        print("✓ Task execution completed")
        print(f"Task result: {result}")
        
        # 检查成功条件
        success = task.check_success()
        print(f"✓ Success check completed: {success}")
        
        print("🎉 All tests passed! The hanging_mug_rim task is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_hanging_mug_rim_task()
    if success:
        print("\n✅ You can now collect data for the hanging_mug_rim task!")
        print("Run: python script/collect_data.py hanging_mug_rim hanging_mug_rim_demo")
    else:
        print("\n⚠️  There are issues with the task implementation. Please check the error messages above.")
