# Basic experiment configuration (keep unchanged)
policy_name: TinyVLA
task_name: place_object_scale
task_config: null
ckpt_setting: null
seed: null
instruction_type: unseen

# Add Parameters You Need
state_path: ~/unet_diffusion_policy_results/place_object_scale-64BS-2e-5LR-8noise_samples/dataset_stats.pkl # 模型训练时生成的统计数据路径，用于后续推理时的标准化处理。 
model_base: ~policy/TinyVLAv2/model_param/InternVL3-1B/ # 基座模型路径
model_path: ~/policy/TinyVLAv2/unet_diffusion_policy_results/place_object_scale-64BS-2e-5LR-8noise_samples/checkpoint-5000 # 模型权重路径
enable_lore: False
setting: NULL
