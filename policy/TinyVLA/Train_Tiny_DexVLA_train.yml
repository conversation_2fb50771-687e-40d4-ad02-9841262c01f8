name: dexvla-robo
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=5.1
  - bzip2=1.0.8
  - ca-certificates=2024.3.11
  - ld_impl_linux-64=2.38
  - libffi=3.4.4
  - libgcc-ng=11.2.0
  - libgomp=11.2.0
  - libstdcxx-ng=11.2.0
  - libuuid=1.41.5
  - ncurses=6.4
  - openssl=3.0.13
  - pip=24.0
  - python=3.10.14
  - readline=8.2
  - setuptools=69.5.1
  - sqlite=3.45.3
  - tk=8.6.14
  - xz=5.4.6
  - zlib=1.2.13
  - pip:
      - absl-py==2.1.0
      - accelerate==1.2.0
      - addict==2.4.0
      - aiofiles==24.1.0
      - aiohttp==3.9.5
      - aiosignal==1.3.1
      - aliyun-python-sdk-core==2.15.1
      - aliyun-python-sdk-kms==2.16.3
      - altair==5.5.0
      - antlr4-python3-runtime==4.9.3
      - anyio==4.9.0
      - asciitree==0.3.3
      - asttokens==3.0.0
      - async-timeout==4.0.3
      - attrs==23.2.0
      - av==14.4.0
      - bitsandbytes==0.41.0
      - certifi==2024.2.2
      - cffi==1.16.0
      - charset-normalizer==3.3.2
      - click==8.2.0
      - conda-pack==0.8.1
      - contourpy==1.2.1
      - crcmod==1.7
      - cryptography==42.0.7
      - cycler==0.12.1
      - datasets==3.2.0
      - decorator==5.2.1
      - deepspeed==0.9.5
      - diffusers==0.33.1
      - dill==0.3.8
      - docker-pycreds==0.4.0
      - draccus==0.11.5
      - einops==0.6.1
      - einops-exts==0.0.4
      - exceptiongroup==1.3.0
      - executing==2.2.0
      - fastapi==0.115.12
      - fasteners==0.19
      - ffmpy==0.5.0
      - filelock==3.14.0
      - flash-attn==2.7.4.post1
      - fonttools==4.51.0
      - frozenlist==1.4.1
      - fsspec==2024.2.0
      - gast==0.5.4
      - gitdb==4.0.12
      - gitpython==3.1.44
      - gradio==3.35.2
      - gradio-client==0.2.9
      - grpcio==1.63.0
      - h11==0.14.0
      - h5py==3.11.0
      - hjson==3.1.0
      - httpcore==0.17.3
      - httpx==0.24.0
      - huggingface-hub==0.31.2
      - hydra-core==1.3.2
      - idna==3.7
      - importlib-metadata==7.1.0
      - ipython==8.36.0
      - jedi==0.19.2
      - jinja2==3.1.4
      - jmespath==0.10.0
      - joblib==1.5.0
      - jsonlines==4.0.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2025.4.1
      - kiwisolver==1.4.5
      - latex2mathml==3.78.0
      - linkify-it-py==2.0.3
      - llava==1.1.1
      - llvmlite==0.44.0
      - markdown==3.6
      - markdown-it-py==2.2.0
      - markdown2==2.5.3
      - markupsafe==2.1.5
      - matplotlib==3.9.0
      - matplotlib-inline==0.1.7
      - mdit-py-plugins==0.3.3
      - mdurl==0.1.2
      - mergedeep==1.3.4
      - modelscope==1.14.0
      - mpmath==1.3.0
      - multidict==6.0.5
      - multiprocess==0.70.16
      - mypy-extensions==1.1.0
      - narwhals==1.39.0
      - networkx==3.3
      - ninja==********
      - numba==0.61.2
      - numcodecs==0.13.1
      - numpy==1.26.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==*********
      - nvidia-cufile-cu12==********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==**********
      - nvidia-cusparse-cu12==**********
      - nvidia-cusparselt-cu12==0.6.3
      - nvidia-nccl-cu12==2.20.5
      - nvidia-nvjitlink-cu12==12.6.85
      - nvidia-nvtx-cu12==12.1.105
      - omegaconf==2.3.0
      - opencv-python==*********
      - opencv-python-headless==*********
      - orjson==3.10.18
      - oss2==2.18.5
      - packaging==24.0
      - pandas==2.2.2
      - parso==0.8.4
      - peft==0.15.2
      - pexpect==4.9.0
      - pillow==10.3.0
      - platformdirs==4.2.2
      - prompt-toolkit==3.0.51
      - protobuf==5.26.1
      - psutil==5.9.8
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - py-cpuinfo==9.0.0
      - pyarrow==16.1.0
      - pyarrow-hotfix==0.6
      - pycparser==2.22
      - pycryptodome==3.20.0
      - pydantic==1.10.22
      - pydub==0.25.1
      - pygments==2.19.1
      - pyparsing==3.1.2
      - python-dateutil==2.9.0.post0
      - python-multipart==0.0.20
      - pytz==2024.1
      - pyyaml==6.0.1
      - pyyaml-include==1.4.1
      - qwen-vl-utils==0.0.11
      - referencing==0.36.2
      - regex==2024.5.15
      - requests==2.32.4
      - rpds-py==0.24.0
      - safetensors==0.4.3
      - scikit-learn==1.2.2
      - scipy==1.13.0
      - semantic-version==2.10.0
      - sentencepiece==0.1.99
      - sentry-sdk==2.28.0
      - setproctitle==1.3.6
      - shortuuid==1.0.13
      - simplejson==3.19.2
      - six==1.16.0
      - smmap==5.0.2
      - sniffio==1.3.1
      - sortedcontainers==2.4.0
      - stack-data==0.6.3
      - starlette==0.46.2
      - svgwrite==1.4.3
      - sympy==1.14.0
      - tensorboard==2.16.2
      - tensorboard-data-server==0.7.2
      - threadpoolctl==3.6.0
      - tiktoken==0.7.0
      - timm==0.9.10
      - tokenizers==0.20.3
      - toml==0.10.2
      - tomli==2.0.1
      - torch==2.3.0
      - torchvision==0.18.0
      - tqdm==4.66.4
      - traitlets==5.14.3
      - transformers==4.45.2
      - transformers-stream-generator==0.0.4
      - triton==2.3.0
      - typing-extensions==4.11.0
      - typing-inspect==0.9.0
      - tzdata==2024.1
      - uc-micro-py==1.0.3
      - urllib3==2.2.1
      - uvicorn==0.34.2
      - wandb==0.19.11
      - wavedrom==2.0.3.post3
      - wcwidth==0.2.13
      - websockets==15.0.1
      - werkzeug==3.0.3
      - wheel==0.41.3
      - xxhash==3.4.1
      - yapf==0.40.2
      - yarl==1.9.4
      - zarr==2.18.3
      - zipp==3.18.2
