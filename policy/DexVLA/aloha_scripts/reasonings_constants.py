TASK_REASONINGS = {
    # '10_13_pot_right_480_640_succ_t0001_s': 'The pot is towards right.',
    # '10_28_pot_right_480_640_succ_t0001_s': 'The pot is towards right.',
    #
    # '10_13_pot_left_480_640_succ_t0001_s': 'The pot is towards left.',
    # '10_28_pot_left_480_640_succ_t0001_s': 'The pot is towards left.',
    #
    # '10_13_pick_tape_new_480_640_succ_t0001_s': 'Sure, there is a tape which can help you paste poster.',
    # '10_27_pick_tape_480_640_succ_t0001_s': 'Sure, there is a tape which can help you paste poster.',
    #
    # '10_13_pick_bread_480_640_succ_t0001_s': 'Sure, there is a bread you can eat.',
    # '10_27_pick_bread_480_640_succ_t0001_s': 'Sure, there is a bread you can eat.',
    #
    # '10_13_pick_pot_480_640_succ_t0001_s': 'There is a kettle you can put water in.',
    # '10_27_pick_kettle_480_640_succ_t0001_s': 'There is a kettle you can put water in.',
    # '10_30_pink_cube_left_blue_box_480_640_succ_t0001_s': 'The blue box lies on the left.',
    # '10_30_pink_cube_right_yellow_box_480_640_succ_t0001_s': 'The yellow box lies on the right.',
    # 'wjj_10_8_open_drawer_place_white_car_480_640': 'Open the drawer first, and put the car in it. Then close the drawer.'

    # '11_1_blue_cube_yellow_box_480_640_succ_t0001_s': 'The box is closed. Remove the lid and put cube into it.',
    # '11_1_blue_cup_bottom_plate_480_640_succ_t0001_s': 'The plate is on the bottom layer.',
    # '11_1_blue_cup_top_plate_480_640_succ_t0001_s': 'The plate is on the top layer.'

    # '10_28_arrange_table_pika_car_480_640': 'The toy pikachu belongs to top-right of box. The toy car belongs to bottom-left of box. The others are unrelated objects.',
    # '10_28_arrange_table_bird_van_480_640': 'The toy bird belongs to top-right of box. The toy van belongs to bottom-left of box. The others are unrelated objects.',

    ###########################aloha#########################################3
    # '1029_place_cup_on_the_shelf':'The teapot is in the cupboard. Open the door and pick it.',
    # '1030_hide_spiderman': 'The drawer is closed. Pull the handle to open it first and put toy spiderman in it.',
    # '1030_magic_cube': "Rotate the right side of rubik's cube to solve it.",
    # '1030_put_light_bulb': 'Okay, install the bulb first and push the button.',
    # '1031_sweep_trash': 'Sweep trash into trash bin with broom and return tools.',
    # '1031_unpack_bag_put_ball':'The bag is closed. Unzip it and put tennis ball in it.'
    # '1105_2358_stack_cup': 'Stack the paper cups into one.',
    'fold_tshirts_zzy_1209': 'The t-shirt is flatten, fold it.',
    'fold_tshirts_129': 'The t-shirt is flatten, fold it.',
    'fold_t_shirt_easy_version': 'The t-shirt is flatten, fold it.',
    'fold_t_shirt_easy_version_office': 'The t-shirt is flatten, fold it.',
    'fold_shirt_zmj1212': 'The t-shirt is flatten, fold it.',
}

TASK_INSTRUCTIONS = {
    # '10_13_pot_right_480_640_succ_t0001_s': 'Upright the tipped-over pot.',
    # '10_28_pot_right_480_640_succ_t0001_s': 'Upright the tipped-over pot.',
    #
    # '10_13_pot_left_480_640_succ_t0001_s': 'Upright the tipped-over pot.',
    # '10_28_pot_left_480_640_succ_t0001_s': 'Upright the tipped-over pot.',
    #
    # '10_13_pick_tape_new_480_640_succ_t0001_s': 'I want to paste a poster, can you help me?',
    # '10_27_pick_tape_480_640_succ_t0001_s': 'I want to paste a poster, can you help me?',
    #
    # '10_13_pick_bread_480_640_succ_t0001_s': 'I am hungry, is there anything I can eat?',
    # '10_27_pick_bread_480_640_succ_t0001_s': 'I am hungry, is there anything I can eat?',
    #
    # '10_13_pick_pot_480_640_succ_t0001_s': 'I want a container to put water in, can you help me?',
    # '10_27_pick_kettle_480_640_succ_t0001_s': 'I want a container to put water in, can you help me?',
    # '10_30_pink_cube_left_blue_box_480_640_succ_t0001_s': 'Put the purple cube into blue box.',
    # '10_30_pink_cube_right_yellow_box_480_640_succ_t0001_s': 'Put the purple cube into yellow box.',
    # 'wjj_10_8_open_drawer_place_white_car_480_640': 'Put the white car into the drawer.'

    # '11_1_blue_cube_yellow_box_480_640_succ_t0001_s': 'Put the blue cube into the yellow box.',
    # '11_1_blue_cup_bottom_plate_480_640_succ_t0001_s': 'Place the blue cup onto the plate.',
    # '11_1_blue_cup_top_plate_480_640_succ_t0001_s': 'Place the blue cup onto the plate.'
    # '10_28_arrange_table_pika_car_480_640': 'Arrange the objects according to their types.',
    # '10_28_arrange_table_bird_van_480_640': 'Arrange the objects according to their types.'
    ###########################aloha#########################################3
    # '1029_place_cup_on_the_shelf': 'I want to make tea. Where is the tea pot?',
    # '1030_hide_spiderman': 'Place the toy spiderman into top drawer.',
    # '1030_magic_cube': "Solve the rubik's cube.",
    # '1030_put_light_bulb': 'Turn on the light.',
    # '1031_sweep_trash': 'Clean the table.',
    # '1031_unpack_bag_put_ball': 'Store the tennis ball into the bag.'
    # '1105_2358_stack_cup': 'Arrange paper cups on the table.',
    'fold_tshirts_zzy_1209': 'Fold t-shirt on the table.',
    'fold_tshirts_129': 'Fold t-shirt on the table.',
    'fold_t_shirt_easy_version': 'Fold t-shirt on the table.',
    'fold_t_shirt_easy_version_office': 'Fold t-shirt on the table.',
    'fold_shirt_zmj1212': 'Fold t-shirt on the table.',
}