# Basic experiment configuration (keep unchanged)
policy_name: DexVLA
task_name: place_object_scale
task_config: null
ckpt_setting: null
seed: null
instruction_type: unseen

# Add Parameters You Need
state_path: ~/unet_diffusion_policy_results/place_object_scale-64BS-2e-5LR-8noise_samples/dataset_stats.pkl # 模型训练时生成的统计数据路径，用于后续推理时的标准化处理。 
model_path: ~/qwen2_vla_aloha/qwen2_vl_3_cameras_1_12_all_data_pretrain_DiT_H_full_param_stage_1_50/checkpoint-60000# 模型路径
model_base: ~policy/DexVLA/model_param/qwenVL-2B/ # 基座模型路径
dit_path: ~policy/policy_step_60000_2025-06-15_09-15-25.ckpt # scaldp路径
model_path: ~/policy/DexVLA/vla_model/place_object_scale-64BS-2e-5LR-8noise_samples/checkpoint-50000 # 模型权重路径
enable_lore: False
setting: NULL
