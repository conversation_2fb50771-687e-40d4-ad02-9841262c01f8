#!/bin/bash
LLM=qwen2_vl   #qwen2_vl  paligemma
LLM_MODEL_SIZE=2B #3B
# LLM_MODEL_SIZE=2_8B
# lora only vit and tune adapter
ACTION_HEAD=dit_diffusion_policy  #act #unet_diffusion_policy dit_diffusion_policy

#echo '1h'
#sleep 1.5h
ROOT=/data/private/joy 
PRETRAIN=${ROOT}/wjj/model_param/multi_head2/${ACTION_HEAD}_results/checkpoint_all/${LLM}_${LLM_MODEL_SIZE}_pure/vanilla_aloha_${LLM}_vla_pt_f_vit/qwen2_vl_all_data_1200_align_frozen_dit_lora_substep_chunk_50/checkpoint-40000 # with substeps DIT
DIT_PRETRAIN=/data/private/policy_step_60000_2025-06-15_09-15-25.ckpt
if [ "${LLM}" == "paligemma" ]; then
  echo "Using PaliGemma"
  mnop=${ROOT}/wjj/model_param/PaliGemma/paligemma/pixel_224/vla-paligemma-3b-pt-224
else
  mnop=${ROOT}/Qwen2-VL-${LLM_MODEL_SIZE}-Instruct # original qwen2vl

fi
############################################################################################################################################
TASKNAME=folding_data_0609
OUTPUT=${ROOT}/dex-checkpoints/stage2/${LLM}_${LLM_MODEL_SIZE}/${TASKNAME}_Stage2_DIT_H_Stage1_1_17_using_state_correct

if [ -d "$OUTPUT" ]; then
   echo 'output exists'
else
   echo '!!output not exists!!'
   mkdir -p $OUTPUT
fi

mkdir -p $OUTPUT/src
cp -r ./aloha_scripts $OUTPUT/src/
cp -r ./scripts $OUTPUT/
cp -r ./data_utils $OUTPUT/src/
cp -r ./dex_vla $OUTPUT/src/
cp -r ./policy_heads $OUTPUT/src/

# tinyvla set "use_reasoning with_llm_head load_pretrain using_film" false
# paligemma flash_attn False

deepspeed --master_port 29604 --num_gpus=8 --num_nodes=1 ./train_vla.py \
  --deepspeed scripts/zero2.json \
  --using_state True \
  --use_reasoning True \
  --external_vision_encoder "None" \
  --lora_enable False \
  --action_dim 14 \
  --state_dim 14 \
  --flash_attn True \
  --chunk_size 50 \
  --lora_module "vit llm" \
  --history_images_length 1 \
  --model_pretrain $PRETRAIN \
  --load_pretrain_dit True \
  --pretrain_dit_path $DIT_PRETRAIN \
  --using_film True \
  --using_ema False \
  --policy_head_type $ACTION_HEAD \
  --policy_head_size "H" \
  --with_llm_head True \
  --image_size_stable "(320,240)" \
  --image_size_wrist "(320,240)" \
  --lora_r 64 \
  --lora_alpha 256 \
  --episode_first False \
  --task_name ${TASKNAME} \
  --model_name_or_path $mnop \
  --version v0 \
  --tune_mm_mlp_adapter True \
  --freeze_vision_tower False \
  --freeze_backbone False \
  --image_aspect_ratio pad \
  --group_by_modality_length False \
  --bf16 True \
  --output_dir $OUTPUT \
  --max_steps 60000 \
  --per_device_train_batch_size 20 \
  --gradient_accumulation_steps 1 \
  --save_strategy "steps" \
  --save_steps 10000 \
  --save_total_limit 50 \
  --learning_rate 2e-5 \
  --weight_decay 0. \
  --warmup_ratio 0.01 \
  --lr_scheduler_type "cosine" \
  --logging_steps 50 \
  --tf32 True \
  --model_max_length 2048 \
  --gradient_checkpointing True \
  --dataloader_num_workers 8 \
  --lazy_preprocess True \
  --policy_class $ACTION_HEAD \
  --concat "token_cat" \
  --report_to tensorboard \
  --logging_dir $OUTPUT/log | tee $OUTPUT/log.log

for dir in "$OUTPUT"/*/ ; do
    # 检查文件夹名称是否包含'checkpoint'
    if [[ "$(basename "$dir")" == *"checkpoint"* ]]; then
        cp ${mnop}/preprocessor_config.json $dir
        cp ${mnop}/chat_template.json $dir
        # cp $OUTPUT/non_lora_trainables.bin $dir
    fi
done
echo $OUTPUT
