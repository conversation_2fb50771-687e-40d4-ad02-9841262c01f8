{"task_name": "hanging_mug_rim", "task_description": "In this task, the robot needs to flip a mug upside down and hang it on a rack by its rim. The robot should grasp the mug, flip it so the opening faces downward, and then carefully place the rim onto the rack's hook.", "task_objects": {"{A}": "mug", "{B}": "rack"}, "task_arms": {"{a}": "the arm holding the mug", "{b}": "the arm assisting with flipping and hanging"}, "basic": ["Hang the mug upside down on the rack by its rim.", "Flip the mug and hang it on the rack opening-down.", "Place the mug rim on the rack hook.", "Invert the mug and hang it on the rack."], "detailed": ["Grasp the {A} with {a}, flip it upside down, and hang it on the {B} by placing the rim on the hook.", "Pick up the {A} with {a}, turn it upside down with {b}, and carefully place the opening onto the {B}.", "Use {a} to hold the {A}, flip it so the opening faces down, then hang the rim on the {B}.", "Grasp the {A} with {a}, invert it with help from {b}, and position the rim to hang on the {B}."], "contextual": ["The mug needs to be hung upside down on the rack. Grasp the {A} with {a}, carefully flip it so the opening faces downward, and hang the rim on the {B}.", "To hang the mug by its rim, use {a} to pick up the {A}, coordinate with {b} to flip it upside down, and place the opening on the {B} hook.", "For this inverted hanging task, hold the {A} with {a}, rotate it 180 degrees so the rim is at the bottom, and hang it on the {B}.", "The goal is to hang the {A} upside down. Use {a} to grasp it, flip it with {b} so the opening points down, and carefully position the rim on the {B}."], "unseen": ["Invert the mug and suspend it from the rack using the rim.", "Turn the mug upside down and hook its opening onto the rack.", "Flip the mug opening-downward and mount it on the rack.", "Rotate the mug 180 degrees and hang it rim-first on the rack."]}