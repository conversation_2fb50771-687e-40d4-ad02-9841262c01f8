from ._base_task import Base_Task
from .utils import *
import numpy as np
from ._GLOBAL_CONFIGS import *
import sapien


class hanging_mug_rim(Base_Task):
    """
    任务：把杯口挂在架子上
    与原始hanging_mug任务的区别：
    - 原任务：通过杯子把手挂在架子上
    - 新任务：通过杯口（rim）挂在架子上，杯子倒置
    """

    def setup_demo(self, is_test=False, **kwags):
        super()._init_task_env_(**kwags)

    def load_actors(self):
        # 随机选择杯子变体
        self.mug_id = np.random.choice([i for i in range(10)])
        
        # 创建杯子，放置在桌面左侧
        self.mug = rand_create_actor(
            self,
            xlim=[-0.25, -0.1],     # X轴：桌面左侧
            ylim=[-0.05, 0.05],     # Y轴：中央区域
            ylim_prop=True,
            modelname="039_mug",
            rotate_rand=True,
            rotate_lim=[0, 1.57, 0], # 允许绕Z轴旋转
            qpos=[0.707, 0.707, 0, 0], # 初始朝向
            convex=True,
            model_id=self.mug_id,
        )

        # 创建架子，放置在桌面右侧
        rack_pose = rand_pose(
            xlim=[0.1, 0.3],        # X轴：桌面右侧
            ylim=[0.13, 0.17],      # Y轴：稍微靠前
            rotate_rand=True,
            rotate_lim=[0, 0.2, 0], # 小幅度旋转
            qpos=[-0.22, -0.22, 0.67, 0.67], # 架子朝向
        )

        self.rack = create_actor(
            self, 
            pose=rack_pose, 
            modelname="040_rack", 
            is_static=True,  # 架子是静态的，不会移动
            convex=True
        )

        # 添加禁止区域，防止机器人初始位置与物体冲突
        self.add_prohibit_area(self.mug, padding=0.1)
        self.add_prohibit_area(self.rack, padding=0.1)
        
        # 定义中间位置，用于双臂协作时的中转
        self.middle_pos = [0.0, -0.15, 0.75, 1, 0, 0, 0]

    def play_once(self):
        """
        主要任务执行逻辑：把杯口挂在架子上
        改进策略：左手抓杯子，右手抓杯底，协调翻转
        """
        # 步骤1：左手抓取杯子（从侧面抓取杯身）
        left_arm = ArmTag("left")
        right_arm = ArmTag("right")

        # 左手抓取杯子的杯身部分
        self.move(self.grasp_actor(
            self.mug,
            arm_tag=left_arm,
            pre_grasp_dis=0.05,
            grasp_dis=0.02,
            functional_point_id=0  # 抓取杯身/把手部分
        ))

        # 举起杯子到合适高度
        self.move(self.move_by_displacement(arm_tag=left_arm, z=0.08))

        # 步骤2：右手从下方抓取杯底
        # 右手移动到杯底位置并抓取
        self.move(self.grasp_actor(
            self.mug,
            arm_tag=right_arm,
            pre_grasp_dis=0.05,
            grasp_dis=0.02,
            functional_point_id=2  # 抓取杯底部分
        ))

        # 步骤3：双手协调翻转杯子
        # 左手保持位置，右手向上推，实现翻转
        # 先将杯子移动到架子附近的翻转位置
        rack_pose = self.rack.get_pose().p
        flip_position = sapien.Pose(
            p=[rack_pose[0] - 0.1, rack_pose[1], rack_pose[2] + 0.15],  # 架子前方上方
            q=[1, 0, 0, 0]
        )

        # 双手协调移动到翻转位置
        self.move(
            self.place_actor(
                self.mug,
                arm_tag=left_arm,
                target_pose=flip_position,
                pre_dis=0.03,
                dis=0.0,
                constrain="free"
            )
        )

        # 步骤4：执行渐进式翻转动作
        # 分阶段翻转，避免突然的大幅度旋转

        # 第一阶段：轻微翻转（30度）
        current_pose = self.mug.get_pose()
        partial_flip_pose = sapien.Pose(
            p=[current_pose.p[0], current_pose.p[1], current_pose.p[2]],
            q=[0.966, 0.259, 0, 0]  # 30度翻转
        )

        self.move(
            self.place_actor(
                self.mug,
                arm_tag=left_arm,
                target_pose=partial_flip_pose,
                pre_dis=0.02,
                dis=0.0,
                constrain="free"
            )
        )

        # 第二阶段：继续翻转到90度
        current_pose = self.mug.get_pose()
        half_flip_pose = sapien.Pose(
            p=[current_pose.p[0], current_pose.p[1], current_pose.p[2]],
            q=[0.707, 0.707, 0, 0]  # 90度翻转
        )

        self.move(
            self.place_actor(
                self.mug,
                arm_tag=left_arm,
                target_pose=half_flip_pose,
                pre_dis=0.02,
                dis=0.0,
                constrain="free"
            )
        )

        # 第三阶段：完全倒置（180度）
        current_pose = self.mug.get_pose()
        inverted_pose = sapien.Pose(
            p=[current_pose.p[0], current_pose.p[1], current_pose.p[2]],
            q=[0, 1, 0, 0]  # 180度翻转，完全倒置
        )

        self.move(
            self.place_actor(
                self.mug,
                arm_tag=left_arm,
                target_pose=inverted_pose,
                pre_dis=0.02,
                dis=0.0,
                constrain="free"
            )
        )

        # 步骤5：右手松开，左手单独控制挂置
        self.move(self.move_by_displacement(arm_tag=right_arm, z=-0.1, move_axis="arm"))

        # 步骤6：将倒置的杯子挂到架子上
        # 获取架子挂钩位置
        rack_hook_pose = self.rack.get_functional_point(0)

        # 计算挂置目标位置
        hang_target = sapien.Pose(
            p=[rack_hook_pose[0], rack_hook_pose[1], rack_hook_pose[2] + 0.03],
            q=[0, 0.707, 0.707, 0]  # 保持倒置状态
        )

        # 精确挂置杯口到架子上
        self.move(
            self.place_actor(
                self.mug,
                arm_tag=left_arm,
                target_pose=hang_target,
                functional_point_id=1,  # 使用杯口功能点
                constrain="align",
                pre_dis=0.03,
                dis=-0.02,  # 稍微插入架子
                pre_dis_axis='fp'
            )
        )

        # 步骤7：松开左手，完成挂置
        self.move(self.move_by_displacement(
            arm_tag=left_arm,
            z=0.08,
            move_axis='arm'
        ))

        # 记录任务信息
        self.info["info"] = {
            "{A}": f"039_mug/base{self.mug_id}",
            "{B}": "040_rack/base0"
        }
        return self.info

    def check_success(self):
        """
        检查任务是否成功完成
        成功条件：
        1. 杯子的杯口（functional_point_id=1）位于架子附近
        2. 杯子是倒置的（杯口朝下）
        3. 机器人手爪已松开
        4. 杯子保持在合适的高度
        """
        try:
            # 获取杯子杯口的位置（functional_point_id=1代表杯口）
            mug_rim_pose = self.mug.get_functional_point(1)[:3]
            
            # 获取架子的位置和功能点
            rack_pose = self.rack.get_pose().p
            rack_function_pose = self.rack.get_functional_point(0)[:3]
            rack_middle_pose = (rack_pose + rack_function_pose) / 2
            
            # 获取杯子的朝向，检查是否倒置
            mug_orientation = self.mug.get_pose().q
            
            # 检查条件
            eps = 0.03  # 位置容差
            
            # 1. 杯口位置接近架子
            position_correct = np.all(abs((mug_rim_pose - rack_middle_pose)[:2]) < eps)
            
            # 2. 杯子在合适的高度（挂在架子上）
            height_correct = mug_rim_pose[2] > 0.85
            
            # 3. 机器人手爪已松开
            gripper_open = self.is_right_gripper_open()
            
            # 4. 检查杯子是否倒置（简化检查：Z分量应该为负）
            # 倒置的杯子其向上向量应该指向下方
            inverted = mug_orientation[1] > 0.5  # 简化的倒置检查
            
            success = position_correct and height_correct and gripper_open and inverted
            
            return success
            
        except Exception as e:
            print(f"Error in check_success: {e}")
            return False
