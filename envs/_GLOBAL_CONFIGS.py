# global configs
import os
import sapien.render

ROOT_PATH = os.path.abspath(__file__)
ROOT_PATH = ROOT_PATH[:ROOT_PATH.rfind("/")]
ROOT_PATH = ROOT_PATH[:ROOT_PATH.rfind("/") + 1]

ASSETS_PATH = os.path.join(ROOT_PATH, "assets/")
EMBODIMENTS_PATH = os.path.join(ASSETS_PATH, "embodiments/")
TEXTURES_PATH = os.path.join(ASSETS_PATH, "background_texture/")
CONFIGS_PATH = os.path.join(ROOT_PATH, "task_config/")
SCRIPT_PATH = os.path.join(ROOT_PATH, "script/")
DESCRIPTION_PATH = os.path.join(ROOT_PATH, "description/")

def setup_sapien_render_config(verbose=False):
    """
    统一配置SAPIEN渲染设置，处理OIDN兼容性问题

    Args:
        verbose (bool): 是否打印详细信息
    """
    sapien.render.set_camera_shader_dir("rt")
    sapien.render.set_ray_tracing_samples_per_pixel(32)
    sapien.render.set_ray_tracing_path_depth(8)

    # 尝试配置OIDN降噪器，处理CUDA兼容性问题
    try:
        # 首先尝试设置为CPU设备类型
        sapien.render.set_ray_tracing_denoiser_device_type("cpu")
        sapien.render.set_ray_tracing_denoiser("oidn")
        if verbose:
            print("OIDN denoiser enabled with CPU device")
    except Exception as e:
        try:
            # 如果CPU设置失败，尝试默认设置
            sapien.render.set_ray_tracing_denoiser("oidn")
            if verbose:
                print("OIDN denoiser enabled with default device")
        except Exception as e2:
            # 如果都失败，禁用降噪器
            if verbose:
                print(f"Warning: OIDN denoiser disabled due to compatibility issues: {e2}")
            # 不设置降噪器，使用默认设置

# Euler angles in world coordinates
# t3d.euler.quat2euler(quat) returns (theta_x, theta_y, theta_z)
# theta_y controls the pitch, and theta_z controls rotation around the axis perpendicular to the tabletop plane
GRASP_DIRECTION_DIC = {
    "left": [0, 0, 0, -1],
    "front_left": [-0.383, 0, 0, -0.924],
    "front": [-0.707, 0, 0, -0.707],
    "front_right": [-0.924, 0, 0, -0.383],
    "right": [-1, 0, 0, 0],
    "top_down": [-0.5, 0.5, -0.5, -0.5],
    "down_right": [-0.707, 0, -0.707, 0],
    "down_left": [0, 0.707, 0, -0.707],
    "top_down_little_left": [-0.353523, 0.61239, -0.353524, -0.61239],
    "top_down_little_right": [-0.61239, 0.353523, -0.61239, -0.353524],
    "left_arm_perf": [-0.853532, 0.146484, -0.353542, -0.3536],
    "right_arm_perf": [-0.353518, 0.353564, -0.14642, -0.853568],
}

WORLD_DIRECTION_DIC = {
    "left": [0, -0.707, 0, 0.707],  # -z  -y  -x
    "front": [0.5, -0.5, 0.5, 0.5],  # y   z   -x
    "right": [0.707, 0, 0.707, 0],  # z   y   -x
    "top_down": [0, 0.707, -0.707, 0],  # -x  -y  -z
}

ROTATE_NUM = 10
