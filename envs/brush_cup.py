from ._base_task import Base_Task
from .utils import *
import sapien
import numpy as np
from ._GLOBAL_CONFIGS import *
import time
import sys

# 添加调试输出
print("Loading brush_cup task module...", file=sys.stderr)
sys.stderr.flush()


class brush_cup(Base_Task):

    def setup_demo(self, **kwags):
        print("Setting up brush_cup demo...", file=sys.stderr)
        sys.stderr.flush()
        super()._init_task_env_(**kwags)

    def load_actors(self):
        # 创建杯子，放置在桌面上的随机位置
        cup_pose = rand_pose(
            xlim=[-0.15, 0.15],
            ylim=[0.05, 0.25],
            zlim=[0.76],  # 桌面高度
            qpos=[1, 0, 0, 0],
            rotate_rand=True,
            rotate_lim=[0, 0, 0.3],  # 允许一定的旋转
        )
        
        # 使用021_cup模型创建杯子
        self.cup = create_actor(
            scene=self,
            pose=cup_pose,
            modelname="021_cup",
            convex=True,
            model_id=0,  # 使用base0变体
        )
        
        # 创建刷子，放置在桌面上的另一个位置
        # 使用更稳定的姿态，减少旋转
        brush_pose = rand_pose(
            xlim=[-0.25, 0.25],
            ylim=[-0.15, 0.05],
            zlim=[0.77],  # 稍微提高一点高度
            qpos=[1, 0, 0, 0],
            rotate_rand=True,
            rotate_lim=[0, 0, 0.1],  # 减少旋转范围
        )
        
        # 确保刷子和杯子不会重叠
        while np.linalg.norm(np.array(brush_pose.p[:2]) - np.array(cup_pose.p[:2])) < 0.15:
            brush_pose = rand_pose(
                xlim=[-0.25, 0.25],
                ylim=[-0.15, 0.05],
                zlim=[0.76],
                qpos=[1, 0, 0, 0],
                rotate_rand=True,
                rotate_lim=[0, 0, 0.5],
            )
        
        # 使用083_brush模型创建刷子
        self.brush = create_actor(
            scene=self,
            pose=brush_pose,
            modelname="083_brush",
            convex=True,
            model_id=0,  # 使用base0变体
        )
        
        # 设置物体质量，增加刷子质量以提高稳定性
        self.cup.set_mass(0.1)
        self.brush.set_mass(0.2)  # 增加刷子质量
        
        # 添加禁止区域，防止机器人初始位置与物体冲突
        self.add_prohibit_area(self.cup, padding=0.08)
        self.add_prohibit_area(self.brush, padding=0.08)

    def play_once(self):
        # 确定使用哪只手臂抓取杯子和刷子
        # 根据物体位置决定：左边的物体用左手，右边的物体用右手
        cup_pos = self.cup.get_pose().p
        brush_pos = self.brush.get_pose().p
        
        # 如果杯子在左边，用左手抓杯子，右手抓刷子
        if cup_pos[0] < brush_pos[0]:
            cup_arm_tag = ArmTag("left")
            brush_arm_tag = ArmTag("right")
        else:
            cup_arm_tag = ArmTag("right")
            brush_arm_tag = ArmTag("left")
        
        # 步骤1：用指定手臂抓取杯子
        self.move(
            self.grasp_actor(
                self.cup,
                arm_tag=cup_arm_tag,
                pre_grasp_dis=0.10,
                grasp_dis=0.02,
                contact_point_id=[0, 1, 2, 3],  # 使用多个接触点稳定抓取
            )
        )
        
        # 步骤2：用另一只手臂抓取刷子
        self.move(
            self.grasp_actor(
                self.brush,
                arm_tag=brush_arm_tag,
                pre_grasp_dis=0.08,
                grasp_dis=0.01,
                functional_point_id=0,  # 抓取刷子的手柄部分
            )
        )
        
        # 步骤3：将杯子举起到合适的高度
        self.move(self.move_by_displacement(cup_arm_tag, z=0.08, move_axis="arm"))
        
        # 步骤4：将刷子移动到杯子内部进行搅拌动作
        # 首先将刷子移动到杯子上方
        cup_center = self.cup.get_pose().p
        brush_target_pose = sapien.Pose(
            [cup_center[0], cup_center[1], cup_center[2] + 0.12],  # 杯子上方
            [1, 0, 0, 0]
        )
        
        self.move(
            self.place_actor(
                self.brush,
                target_pose=brush_target_pose,
                arm_tag=brush_arm_tag,
                functional_point_id=1,  # 使用刷子的刷头部分
                pre_dis=0.05,
                dis=0.02,
                is_open=False,
            )
        )
        
        # 步骤5：将刷子插入杯子内部
        self.move(self.move_by_displacement(brush_arm_tag, z=-0.06, move_axis="arm"))
        
        # 步骤6：执行搅拌动作 - 在杯子内做圆周运动
        # 执行几个小的圆周运动来模拟刷洗
        for i in range(3):
            # 向前移动
            self.move(self.move_by_displacement(brush_arm_tag, y=0.02, move_axis="arm"))
            # 向右移动
            self.move(self.move_by_displacement(brush_arm_tag, x=0.02, move_axis="arm"))
            # 向后移动
            self.move(self.move_by_displacement(brush_arm_tag, y=-0.02, move_axis="arm"))
            # 向左移动
            self.move(self.move_by_displacement(brush_arm_tag, x=-0.02, move_axis="arm"))
        
        # 步骤7：将刷子从杯子中取出
        self.move(self.move_by_displacement(brush_arm_tag, z=0.08, move_axis="arm"))
        
        # 记录任务信息
        self.info["info"] = {
            "{A}": "021_cup/base0",  # 杯子模型
            "{B}": "083_brush/base0",  # 刷子模型
            "{a}": str(cup_arm_tag),  # 抓杯子的手臂
            "{b}": str(brush_arm_tag),  # 抓刷子的手臂
        }
        return self.info

    def check_success(self):
        # 成功条件：
        # 1. 杯子被抓住且保持在合适高度
        # 2. 刷子被抓住
        # 3. 刷子曾经进入过杯子内部（通过检查刷子和杯子的距离）

        try:
            cup_pos = self.cup.get_pose().p
            brush_pos = self.brush.get_pose().p

            # 检查杯子是否被举起（高度检查）
            cup_lifted = cup_pos[2] > 0.82  # 比桌面高度高一些

            # 检查刷子和杯子的距离，判断是否进行了刷洗动作
            distance = np.linalg.norm(brush_pos - cup_pos)
            brush_near_cup = distance < 0.15  # 刷子应该靠近杯子

            # 检查两个物体都被机器人抓住
            cup_grasped = self.check_actor_grasped(self.cup)
            brush_grasped = self.check_actor_grasped(self.brush)

            success = cup_lifted and brush_near_cup and cup_grasped and brush_grasped
            print(f"Success check: cup_lifted={cup_lifted}, brush_near_cup={brush_near_cup}, cup_grasped={cup_grasped}, brush_grasped={brush_grasped}, overall={success}", file=sys.stderr)
            return success
        except Exception as e:
            print(f"Error in check_success: {e}", file=sys.stderr)
            return False
