# 挂杯口任务配置文件
# 任务：将杯子倒置，通过杯口挂在架子上

# 基础任务配置
task_name: hanging_mug_rim
embodiment: ['aloha-agilex']

# 环境配置
messy_table: true
random_background: true
clean_background_rate: 0.02
random_light: true
crazy_random_light_rate: 0.02
random_table_height: 0.03
random_head_camera_distance: 0

# 相机配置
head_camera_config:
  camera_type: D435
  enable: true

wrist_camera_config:
  camera_type: D435
  enable: true

# 数据收集配置
data_collection:
  num_episodes: 100
  max_steps_per_episode: 200
  success_threshold: 0.8

# 域随机化配置
domain_randomization:
  object_pose_noise: 0.02
  lighting_variation: true
  texture_randomization: true
  physics_noise: 0.01

# 任务特定配置
task_specific:
  mug_variants: 10  # 杯子变体数量
  rack_position_variance: 0.05  # 架子位置变化范围
  flip_precision: 0.02  # 翻转精度要求
  hanging_tolerance: 0.03  # 挂置容差
