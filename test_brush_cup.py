#!/usr/bin/env python3
"""
测试刷杯子任务的简单脚本
"""
import sys
sys.path.append("./")

import sapien.core as sapien
from envs.brush_cup import brush_cup
import yaml

def test_brush_cup_task():
    """测试刷杯子任务是否正常工作"""
    print("Testing brush_cup task...")
    
    try:
        # 创建任务实例
        task = brush_cup()
        print("✓ Task instance created successfully")
        
        # 加载配置
        with open("task_config/brush_cup_demo.yml", "r") as f:
            config = yaml.load(f.read(), Loader=yaml.FullLoader)
        
        config['task_name'] = 'brush_cup'
        config['embodiment'] = ['aloha-agilex']
        
        print("✓ Configuration loaded successfully")
        
        # 设置演示环境
        task.setup_demo(**config)
        print("✓ Demo environment setup completed")
        
        # 执行一次任务
        result = task.play_once()
        print("✓ Task execution completed")
        print(f"Task result: {result}")
        
        # 检查成功条件
        success = task.check_success()
        print(f"✓ Success check completed: {success}")
        
        print("🎉 All tests passed! The brush_cup task is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_brush_cup_task()
    if success:
        print("\n✅ You can now collect data for the brush_cup task!")
    else:
        print("\n⚠️  There are issues with the task implementation. Please check the error messages above.")
